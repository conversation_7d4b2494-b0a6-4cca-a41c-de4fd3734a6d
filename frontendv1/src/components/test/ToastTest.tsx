import React from 'react';
import { toast } from 'react-toastify';
import { showNotificationToast } from '../notifications/NotificationToast';
import { AppNotification, NotificationPriority } from '../../types/notifications';

const ToastTest: React.FC = () => {
  const testBasicToasts = () => {
    toast.success('Your changes have been saved successfully!');
    setTimeout(() => toast.error('Failed to connect to the server. Please try again.'), 500);
    setTimeout(() => toast.info('New features are now available in your dashboard.'), 1000);
    setTimeout(() => toast.warning('Your session will expire in 5 minutes.'), 1500);
  };

  const testNotificationToasts = () => {
    const mockNotifications: AppNotification[] = [
      {
        id: 1,
        title: 'Training Assignment',
        message: 'You have been assigned a new safety training course that must be completed by Friday.',
        type: 'training_assigned',
        priority: NotificationPriority.HIGH,
        createdAt: new Date().toISOString(),
        readAt: null,
        status: 'UNREAD' as any,
        actionUrl: '/training',
        actionLabel: 'View Training'
      },
      {
        id: 2,
        title: 'System Maintenance',
        message: 'Scheduled maintenance will occur tonight from 2:00 AM to 4:00 AM EST.',
        type: 'system_alert',
        priority: NotificationPriority.MEDIUM,
        createdAt: new Date().toISOString(),
        readAt: null,
        status: 'UNREAD' as any,
        actionUrl: null,
        actionLabel: null
      },
      {
        id: 3,
        title: 'Certificate Expiring',
        message: 'Your safety certification expires in 30 days. Please renew to maintain compliance.',
        type: 'permit_expiring',
        priority: NotificationPriority.CRITICAL,
        createdAt: new Date().toISOString(),
        readAt: null,
        status: 'UNREAD' as any,
        actionUrl: '/certificates',
        actionLabel: 'Renew Now'
      }
    ];

    mockNotifications.forEach((notification, index) => {
      setTimeout(() => {
        showNotificationToast(notification);
      }, index * 800);
    });
  };

  return (
    <div className="p-8 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-6 text-gray-900">Modern Toast Notification Test</h2>

      <div className="space-y-4">
        <div className="bg-white rounded-lg p-6 shadow-sm border">
          <h3 className="text-lg font-semibold mb-3 text-gray-800">Basic Toast Types</h3>
          <p className="text-gray-600 mb-4">Test the new glassmorphism styling for standard toast notifications.</p>
          <button
            onClick={testBasicToasts}
            className="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium shadow-sm"
          >
            Test Basic Toasts
          </button>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border">
          <h3 className="text-lg font-semibold mb-3 text-gray-800">Notification Toasts</h3>
          <p className="text-gray-600 mb-4">Test the redesigned notification toast component with rich content.</p>
          <button
            onClick={testNotificationToasts}
            className="px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200 font-medium shadow-sm"
          >
            Test Notification Toasts
          </button>
        </div>

        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-6 border border-purple-200">
          <h3 className="text-lg font-semibold mb-2 text-purple-900">Design Features</h3>
          <ul className="text-sm text-purple-800 space-y-1">
            <li>✨ Glassmorphism with backdrop blur effects</li>
            <li>🎨 Professional color schemes for each notification type</li>
            <li>🚫 No borders (as requested)</li>
            <li>📱 Responsive design for mobile and desktop</li>
            <li>♿ Accessibility features and reduced motion support</li>
            <li>🌙 Dark mode compatibility</li>
            <li>⚡ Smooth spring animations with stagger effects</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ToastTest;
