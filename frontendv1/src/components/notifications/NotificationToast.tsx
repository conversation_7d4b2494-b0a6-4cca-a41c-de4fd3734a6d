import React from 'react';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import {
  Bell,
  Clock,
  User,
  AlertTriangle,
  CheckCircle,
  Calendar,
  AlertCircle,
  X
} from 'lucide-react';
import { AppNotification, NotificationPriority } from '../../types/notifications';

interface NotificationToastProps {
  notification: AppNotification;
  onClose?: () => void;
  onMarkAsRead?: (id: number) => void;
}

const NotificationToast: React.FC<NotificationToastProps> = ({
  notification,
  onClose,
  onMarkAsRead
}) => {
  const getNotificationIcon = (type: string) => {
    const iconSize = "h-6 w-6";
    switch (type) {
      case 'training_expiring':
        return <Clock className={iconSize} />;
      case 'training_expired':
        return <AlertTriangle className={iconSize} />;
      case 'training_assigned':
      case 'training_completed':
      case 'worker_added':
        return <User className={iconSize} />;
      case 'system_alert':
        return <AlertCircle className={iconSize} />;
      case 'permit_expiring':
      case 'permit_expired':
        return <CheckCircle className={iconSize} />;
      case 'safety_incident':
        return <AlertTriangle className={iconSize} />;
      case 'reminder':
        return <Calendar className={iconSize} />;
      default:
        return <Bell className={iconSize} />;
    }
  };

  const getPriorityStyles = (priority: NotificationPriority) => {
    switch (priority) {
      case NotificationPriority.CRITICAL:
        return { iconColor: 'text-red-600' };
      case NotificationPriority.HIGH:
        return { iconColor: 'text-orange-600' };
      case NotificationPriority.MEDIUM:
        return { iconColor: 'text-yellow-600' };
      case NotificationPriority.LOW:
        return { iconColor: 'text-blue-600' };
      default:
        return { iconColor: 'text-gray-600' };
    }
  };

  const priorityStyles = getPriorityStyles(notification.priority);

  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onMarkAsRead) {
      onMarkAsRead(notification.id);
    }
  };

  const handleClose = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onClose) {
      onClose();
    }
  };

  const navigate = useNavigate();

  const handleClick = () => {
    if (notification.actionUrl) {
      navigate(notification.actionUrl);
    }
  };

  return (
    <div
      className={`
        relative w-full max-w-sm md:max-w-md
        bg-white/85 backdrop-blur-xl
        rounded-2xl border border-white/20
        shadow-[0_20px_25px_-5px_rgba(0,0,0,0.08),0_10px_10px_-5px_rgba(0,0,0,0.04)]
        hover:shadow-[0_25px_35px_-5px_rgba(0,0,0,0.12),0_15px_15px_-5px_rgba(0,0,0,0.06)]
        hover:-translate-y-0.5
        transition-all duration-300 ease-out
        cursor-pointer
        ${notification.actionUrl ? 'hover:bg-white/90' : ''}
      `}
      onClick={handleClick}
      role="alert"
      aria-live="polite"
      aria-labelledby={`toast-title-${notification.id}`}
      aria-describedby={`toast-message-${notification.id}`}
    >
      {/* Close button */}
      <button
        onClick={handleClose}
        className="
          absolute top-4 right-4 z-10
          w-8 h-8 flex items-center justify-center
          bg-white/30 backdrop-blur-sm
          border border-white/20 rounded-lg
          text-gray-500 hover:text-gray-700
          hover:bg-white/50 hover:scale-105
          transition-all duration-200 ease-out
          focus:outline-none focus:ring-2 focus:ring-blue-500/30
        "
        aria-label="Close notification"
      >
        <X className="h-4 w-4" />
      </button>

      <div className="flex items-start gap-4 p-6 pr-14">
        {/* Icon */}
        <div className={`
          flex-shrink-0 p-3 rounded-xl
          bg-white/40 backdrop-blur-sm
          border border-white/30
          shadow-[0_4px_6px_-1px_rgba(0,0,0,0.05)]
          ${priorityStyles.iconColor}
        `}>
          {getNotificationIcon(notification.type)}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <h4
            id={`toast-title-${notification.id}`}
            className="text-base font-semibold text-gray-900 mb-2 leading-tight"
          >
            {notification.title}
          </h4>

          <p
            id={`toast-message-${notification.id}`}
            className="text-sm text-gray-700 leading-relaxed mb-4"
          >
            {notification.message}
          </p>

          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-500 font-medium">
              Just now
            </span>

            <div className="flex items-center gap-3">
              {notification.actionLabel && (
                <span className="
                  inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-medium
                  bg-gradient-to-r from-green-50 to-emerald-50
                  text-green-700 border border-green-200/50
                  shadow-sm
                ">
                  {notification.actionLabel}
                </span>
              )}

              {!notification.readAt && (
                <button
                  onClick={handleMarkAsRead}
                  className="
                    text-xs text-gray-500 hover:text-gray-700
                    font-medium px-2 py-1 rounded-md
                    hover:bg-white/50 transition-colors duration-200
                    focus:outline-none focus:ring-2 focus:ring-blue-500/30
                  "
                >
                  Mark as read
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Toast notification functions
export const showNotificationToast = (
  notification: AppNotification,
  onMarkAsRead?: (id: number) => void
) => {
  const toastId = `notification-${notification.id}`;

  // Deduplicate: if a toast with this ID is already active, do not show another
  if (toast.isActive(toastId)) {
    return;
  }

  // Determine toast type based on priority
  const toastType = (() => {
    switch (notification.priority) {
      case NotificationPriority.CRITICAL:
        return 'error';
      case NotificationPriority.HIGH:
        return 'warning';
      case NotificationPriority.MEDIUM:
        return 'info';
      case NotificationPriority.LOW:
      default:
        return 'default';
    }
  })();

  // Determine auto-close delay based on priority
  const autoClose = (() => {
    switch (notification.priority) {
      case NotificationPriority.CRITICAL:
        return false; // Don't auto-close critical notifications
      case NotificationPriority.HIGH:
        return 10000; // 10 seconds
      case NotificationPriority.MEDIUM:
        return 7000; // 7 seconds
      case NotificationPriority.LOW:
      default:
        return 5000; // 5 seconds
    }
  })();

  toast(
    <NotificationToast
      notification={notification}
      onMarkAsRead={onMarkAsRead}
      onClose={() => toast.dismiss(toastId)}
    />,
    {
      toastId,
      type: toastType,
      autoClose,
      closeButton: false,
      className: 'notification-toast',

      hideProgressBar: true,
      position: 'top-right',
    }
  );
};

export const dismissNotificationToast = (notificationId: number) => {
  toast.dismiss(`notification-${notificationId}`);
};

export default NotificationToast;
