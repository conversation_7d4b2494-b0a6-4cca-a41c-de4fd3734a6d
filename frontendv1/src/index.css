@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 142.1 76.2% 36.3%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 210 40% 98%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 98%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 98%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 142.1 76.2% 36.3%;
    --radius: 0.5rem;
    /* Extended semantic colors for toasts */
    --success: 142.1 76.2% 36.3%;
    --warning: 32 95% 44%;
    --info: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Modern Glassmorphism Toast Notification Styles */
.notification-toast {
  padding: 0 !important;
  background: transparent !important;
  box-shadow: none !important;
  border-radius: 16px !important;
}

.notification-toast .Toastify__toast-body {
  padding: 0 !important;
  margin: 0 !important;
}

/* Modern Glassmorphism Toast Styling System */
.app-toast .Toastify__toast {
  /* Glassmorphism base */
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);

  /* Modern styling */
  color: rgba(17, 24, 39, 0.95);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 20px 24px;
  line-height: 1.6;
  font-weight: 500;
  font-size: 15px;

  /* Professional shadows */
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.08),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);

  /* Smooth animations */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
}

.app-toast .Toastify__toast:hover {
  transform: translateY(-2px);
  box-shadow:
    0 25px 35px -5px rgba(0, 0, 0, 0.12),
    0 15px 15px -5px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);
}

.app-toast .Toastify__toast-body {
  margin: 0;
  padding: 0;
  gap: 16px;
  align-items: flex-start;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.app-toast .Toastify__toast-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: 10px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  flex-shrink: 0;
}

.app-toast .Toastify__toast-icon svg {
  width: 20px;
  height: 20px;
}

/* Modern Close Button */
.app-toast .Toastify__close-button {
  color: rgba(107, 114, 128, 0.8);
  opacity: 1;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.app-toast .Toastify__close-button:hover {
  color: rgba(17, 24, 39, 0.9);
  background: rgba(255, 255, 255, 0.5);
  transform: scale(1.05);
  box-shadow: 0 4px 8px -2px rgba(0, 0, 0, 0.1);
}

/* Professional Type-Specific Variants */
.app-toast .Toastify__toast--success {
  background: rgba(236, 253, 245, 0.9);
  border: 1px solid rgba(16, 185, 129, 0.2);
  box-shadow:
    0 20px 25px -5px rgba(16, 185, 129, 0.12),
    0 10px 10px -5px rgba(16, 185, 129, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);
}

.app-toast .Toastify__toast--success .Toastify__toast-icon {
  color: #059669;
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.app-toast .Toastify__toast--error {
  background: rgba(254, 242, 242, 0.9);
  border: 1px solid rgba(239, 68, 68, 0.2);
  box-shadow:
    0 20px 25px -5px rgba(239, 68, 68, 0.12),
    0 10px 10px -5px rgba(239, 68, 68, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);
}

.app-toast .Toastify__toast--error .Toastify__toast-icon {
  color: #DC2626;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.app-toast .Toastify__toast--warning {
  background: rgba(255, 251, 235, 0.9);
  border: 1px solid rgba(245, 158, 11, 0.2);
  box-shadow:
    0 20px 25px -5px rgba(245, 158, 11, 0.12),
    0 10px 10px -5px rgba(245, 158, 11, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);
}

.app-toast .Toastify__toast--warning .Toastify__toast-icon {
  color: #D97706;
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.app-toast .Toastify__toast--info {
  background: rgba(239, 246, 255, 0.9);
  border: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow:
    0 20px 25px -5px rgba(59, 130, 246, 0.12),
    0 10px 10px -5px rgba(59, 130, 246, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);
}

.app-toast .Toastify__toast--info .Toastify__toast-icon {
  color: #2563EB;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Modern Progress Bar Styling */
.app-toast .Toastify__progress-bar {
  height: 3px;
  border-radius: 0 0 16px 16px;
  background: linear-gradient(90deg, rgba(255,255,255,0.3), rgba(255,255,255,0.6));
}

.app-toast .Toastify__progress-bar--success {
  background: linear-gradient(90deg, #10B981, #059669);
}
.app-toast .Toastify__progress-bar--error {
  background: linear-gradient(90deg, #EF4444, #DC2626);
}
.app-toast .Toastify__progress-bar--warning {
  background: linear-gradient(90deg, #F59E0B, #D97706);
}
.app-toast .Toastify__progress-bar--info {
  background: linear-gradient(90deg, #3B82F6, #2563EB);
}

/* Responsive Design */
@media (max-width: 640px) {
  .app-toast .Toastify__toast {
    padding: 16px 20px;
    border-radius: 14px;
    font-size: 14px;
  }

  .app-toast .Toastify__toast-body {
    gap: 12px;
  }

  .app-toast .Toastify__toast-icon {
    padding: 8px;
    border-radius: 10px;
  }

  .app-toast .Toastify__toast-icon svg {
    width: 18px;
    height: 18px;
  }

  .app-toast .Toastify__close-button {
    width: 28px;
    height: 28px;
    border-radius: 6px;
  }
}

/* Professional Animation System */
.Toastify__toast-container .Toastify__toast {
  animation-duration: 0.5s;
  animation-timing-function: cubic-bezier(0.34, 1.56, 0.64, 1);
  animation-fill-mode: both;
}

.Toastify__toast-container .Toastify__toast--rtl {
  animation-name: modernSlideInRight;
}

.Toastify__toast-container .Toastify__toast--ltr {
  animation-name: modernSlideInLeft;
}

/* Exit animations */
.Toastify__toast-container .Toastify__toast.Toastify__toast--close {
  animation-name: modernSlideOut;
  animation-duration: 0.3s;
  animation-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

/* Modern entrance animations with spring effect */
@keyframes modernSlideInRight {
  0% {
    transform: translate3d(120%, 0, 0) scale(0.9) rotateY(15deg);
    opacity: 0;
    filter: blur(4px);
  }
  60% {
    transform: translate3d(-8%, 0, 0) scale(1.02) rotateY(-2deg);
    opacity: 0.9;
    filter: blur(1px);
  }
  100% {
    transform: translate3d(0, 0, 0) scale(1) rotateY(0deg);
    opacity: 1;
    filter: blur(0px);
  }
}

@keyframes modernSlideInLeft {
  0% {
    transform: translate3d(-120%, 0, 0) scale(0.9) rotateY(-15deg);
    opacity: 0;
    filter: blur(4px);
  }
  60% {
    transform: translate3d(8%, 0, 0) scale(1.02) rotateY(2deg);
    opacity: 0.9;
    filter: blur(1px);
  }
  100% {
    transform: translate3d(0, 0, 0) scale(1) rotateY(0deg);
    opacity: 1;
    filter: blur(0px);
  }
}

/* Smooth exit animation */
@keyframes modernSlideOut {
  0% {
    transform: translate3d(0, 0, 0) scale(1);
    opacity: 1;
    filter: blur(0px);
  }
  100% {
    transform: translate3d(120%, 0, 0) scale(0.9);
    opacity: 0;
    filter: blur(2px);
  }
}

/* Micro-interactions for enhanced UX */
.app-toast .Toastify__toast-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.app-toast .Toastify__toast:hover .Toastify__toast-icon {
  transform: scale(1.05) rotate(2deg);
  box-shadow:
    0 6px 12px -2px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* Stagger animation for multiple toasts */
.Toastify__toast-container .Toastify__toast:nth-child(1) {
  animation-delay: 0ms;
}
.Toastify__toast-container .Toastify__toast:nth-child(2) {
  animation-delay: 100ms;
}
.Toastify__toast-container .Toastify__toast:nth-child(3) {
  animation-delay: 200ms;
}
.Toastify__toast-container .Toastify__toast:nth-child(4) {
  animation-delay: 300ms;
}

/* Accessibility and Polish */
@media (prefers-reduced-motion: reduce) {
  .app-toast .Toastify__toast,
  .app-toast .Toastify__toast-icon,
  .app-toast .Toastify__close-button {
    animation: none !important;
    transition: none !important;
  }

  .Toastify__toast-container .Toastify__toast {
    animation: fadeIn 0.2s ease-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
}

/* Focus states for accessibility */
.app-toast .Toastify__toast:focus-within {
  outline: 2px solid #3B82F6;
  outline-offset: 2px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .app-toast .Toastify__toast {
    background: rgba(17, 24, 39, 0.9);
    color: rgba(243, 244, 246, 0.95);
    border: 1px solid rgba(75, 85, 99, 0.3);
  }

  .app-toast .Toastify__toast-icon {
    background: rgba(31, 41, 55, 0.6);
    border: 1px solid rgba(75, 85, 99, 0.3);
  }

  .app-toast .Toastify__close-button {
    background: rgba(31, 41, 55, 0.6);
    border: 1px solid rgba(75, 85, 99, 0.3);
    color: rgba(156, 163, 175, 0.8);
  }

  .app-toast .Toastify__close-button:hover {
    background: rgba(55, 65, 81, 0.8);
    color: rgba(243, 244, 246, 0.9);
  }
}

:root {
	font-family: "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI",
		Roboto, "Helvetica Neue", Arial, sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

body {
	@apply bg-[#eaede8] text-gray-800;
	margin: 0;
	padding: 0;
}

/* Custom scrollbar */
::-webkit-scrollbar {
	width: 8px;
	height: 8px;
}

::-webkit-scrollbar-track {
	background: transparent;
}

::-webkit-scrollbar-thumb {
	background-color: rgba(156, 163, 175, 0.5);
	border-radius: 20px;
}

::-webkit-scrollbar-thumb:hover {
	background-color: rgba(156, 163, 175, 0.7);
}

/* Hide scrollbar utility class */
.scrollbar-hide {
	-ms-overflow-style: none; /* Internet Explorer 10+ */
	scrollbar-width: none; /* Firefox */
}

/* Calendar scrollbar styles */
.scrollbar-thin {
	scrollbar-width: thin;
	scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
	width: 6px;
	height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
	background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
	background-color: rgba(156, 163, 175, 0.3);
	border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
	background-color: rgba(156, 163, 175, 0.5);
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
	background-color: rgba(156, 163, 175, 0.3);
}

.scrollbar-track-transparent::-webkit-scrollbar-track {
	background: transparent;
}

/* Responsive grid utilities for permits */
@media (max-width: 639px) {
	/* Extra small screens - ensure single column with proper spacing */
	.permits-grid {
		grid-template-columns: 1fr;
		gap: 1rem;
	}
}

@media (min-width: 640px) and (max-width: 1023px) {
	/* Small to medium screens - 2 columns */
	.permits-grid {
		grid-template-columns: repeat(2, 1fr);
		gap: 1.5rem;
	}
}

@media (min-width: 1024px) and (max-width: 1279px) {
	/* Large screens (13" laptops) - 3 columns */
	.permits-grid {
		grid-template-columns: repeat(3, 1fr);
		gap: 1.5rem;
	}
}

@media (min-width: 1280px) {
	/* Extra large screens - 4 columns */
	.permits-grid {
		grid-template-columns: repeat(4, 1fr);
		gap: 1.5rem;
	}
}

.scrollbar-hide::-webkit-scrollbar {
	display: none; /* Safari and Chrome */
}

/* Flyout menu animations and styling */
.flyout-menu {
	/* Ensure flyout appears as seamless extension */
	border-left: 1px solid rgba(229, 231, 235, 0.3);
}

/* Step Sidebar Styles - Clean design with proper alignment */
.step-sidebar {
	@apply w-80 flex-shrink-0;
	background-color: #fdfdf9;
	border-right: 1px solid #e5e7eb;
	height: 100vh; /* Full viewport height to run separator all the way through */
}

.step-list {
	@apply p-6;
}

.step-item {
	@apply flex items-center justify-between mb-8 last:mb-0;
	min-height: 3rem; /* Ensure consistent height for alignment */
}

.step-item:hover .step-title {
	color: #22c55e;
	transition: color 0.2s ease-in-out;
}

.step-item:hover .step-description {
	color: #22c55e;
	transition: color 0.2s ease-in-out;
}

.step-content {
	@apply flex-1 text-right pr-4;
}

.step-title {
	@apply text-sm font-medium leading-tight text-gray-900;
	transition: color 0.2s ease-in-out;
}

.step-description {
	@apply text-xs mt-1 leading-relaxed text-gray-600;
	transition: color 0.2s ease-in-out;
}

.step-circle {
	@apply w-9 h-9 rounded-full flex items-center justify-center flex-shrink-0 transition-all duration-200 font-medium text-sm;
	border: 2px solid;
}

.step-circle.completed {
	background-color: #22c55e;
	border-color: #22c55e;
	color: white;
}

.step-circle.current {
	background-color: #3b82f6;
	border-color: #3b82f6;
	color: white;
}

.step-circle.error {
	background-color: #ef4444;
	border-color: #ef4444;
	color: white;
}

.step-circle.pending {
	background-color: white;
	border-color: #d1d5db;
	color: #6b7280;
}

/* Main content area for site creation */
.main-content {
	@apply flex-1 p-8 overflow-y-auto;
}

.form-container {
	background-color: #fdfdf9;
	@apply rounded-lg border border-gray-200 shadow-sm;
}

/* Flyout menu item hover styling */
.flyout-menu-item:hover:not(.active) {
	background-color: #fdfdf9 !important;
	color: #22c55e !important; /* green-500 */
}

.flyout-enter {
	transform: translateX(-100%);
	opacity: 0;
}

.flyout-enter-active {
	transform: translateX(0);
	opacity: 1;
	transition: transform 300ms ease-out, opacity 300ms ease-out;
}

.flyout-exit {
	transform: translateX(0);
	opacity: 1;
}

.flyout-exit-active {
	transform: translateX(-100%);
	opacity: 0;
	transition: transform 250ms ease-in, opacity 250ms ease-in;
}
